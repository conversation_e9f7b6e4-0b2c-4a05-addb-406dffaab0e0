using System.Globalization;
using System.Threading;
using System.Windows;
using MassStorageStableTestTool.UI.Resources;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MassStorageStableTestTool.UI.ViewModels;

namespace MassStorageStableTestTool.UI;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    public IServiceProvider ServiceProvider { get; private set; } = null!;

    private const string LangConfigFile = "lang.config";

    protected override void OnStartup(StartupEventArgs e)
    {
        // 启动时读取语言配置
        string lang = CultureInfo.InstalledUICulture.Name.StartsWith("zh", StringComparison.OrdinalIgnoreCase) ? "zh-CN" : "en-US";
        if (System.IO.File.Exists(LangConfigFile))
        {
            try { lang = System.IO.File.ReadAllText(LangConfigFile).Trim(); } catch { }
        }
        SetLanguage(lang);

        try
        {
            Console.WriteLine("开始启动应用程序...");
            var serviceCollection = new ServiceCollection();
            Console.WriteLine("创建服务集合完成");
            ConfigureServices(serviceCollection);
            Console.WriteLine("配置服务完成");
            ServiceProvider = serviceCollection.BuildServiceProvider();
            Console.WriteLine("构建服务提供者完成");
            var mainWindow = ServiceProvider.GetRequiredService<MainWindow>();
            Console.WriteLine("获取MainWindow完成");
            mainWindow.Show();
            Console.WriteLine("显示主窗口完成");
            base.OnStartup(e);
            Console.WriteLine("应用程序启动成功");
        }
        catch (Exception ex)
        {
            var errorMessage = $"应用程序启动失败: {ex.Message}\n\n详细信息:\n{ex}";
            Console.WriteLine(errorMessage);
            // 写入到文件以便调试
            try { System.IO.File.WriteAllText("startup_error.log", errorMessage); } catch { }
            MessageBox.Show(errorMessage, "启动错误", MessageBoxButton.OK, MessageBoxImage.Error);
            Shutdown();
        }
    }

    private void ConfigureServices(IServiceCollection services)
    {
        // 日志服务
        services.AddLogging(builder =>
        {
            builder.SetMinimumLevel(Microsoft.Extensions.Logging.LogLevel.Information);
        });

        // ViewModels
        services.AddTransient<MainViewModel>();

        // Views
        services.AddTransient<MainWindow>();
    }

    public static void SetLanguage(string cultureName)
    {
        var culture = new CultureInfo(cultureName);
        Thread.CurrentThread.CurrentCulture = culture;
        Thread.CurrentThread.CurrentUICulture = culture;
        Strings.Culture = culture;
        // 切换语言时写入配置文件
        try { System.IO.File.WriteAllText(LangConfigFile, cultureName); } catch { }
    }

    protected override void OnExit(ExitEventArgs e)
    {
        if (ServiceProvider is IDisposable disposable)
        {
            disposable.Dispose();
        }
        base.OnExit(e);
    }
}

